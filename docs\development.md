# 开发指南

<!-- 章节统计信息 -->
<div style="background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%); border-left: 4px solid #3fcfbb; padding: 15px 20px; margin: 20px 0; border-radius: 8px;">
  <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
    <span style="color: #2aa698; font-weight: 600; font-size: 0.9em;">📊 本章统计</span>
    <span style="background: #3fcfbb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">字数: ~5,200</span>
    <span style="background: #33cabb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">阅读时长: ~18分钟</span>
    <span style="background: #2aa698; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">难度: 高级</span>
  </div>
</div>

本指南面向希望基于易华CCP进行二次开发或集成的开发者，提供详细的开发环境搭建、架构说明和开发规范。

## 开发环境搭建

### 环境要求

- **Java**: OpenJDK 11+ 或 Oracle JDK 11+
- **Node.js**: 14.x 或更高版本
- **Maven**: 3.6+ (Java项目构建)
- **npm/yarn**: 包管理工具
- **Git**: 版本控制
- **IDE**: IntelliJ IDEA 或 VS Code

### 获取源码

```bash
# 克隆主仓库
git clone https://github.com/yihua-ccp/yihua-ccp.git
cd yihua-ccp

# 克隆前端仓库
git clone https://github.com/yihua-ccp/yihua-ccp-frontend.git
```

### 后端开发环境

#### 1. 安装依赖

```bash
cd yihua-ccp
mvn clean install
```

#### 2. 配置数据库

创建开发数据库：

```sql
CREATE DATABASE yihua_ccp_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

配置文件 `src/main/resources/application-dev.yml`：

```yaml
spring:
  datasource:
    url: *****************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  
  redis:
    host: localhost
    port: 6379
    database: 1

logging:
  level:
    com.yihua.ccp: DEBUG
```

#### 3. 启动后端服务

```bash
# 使用Maven启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或使用IDE直接运行Application.java
```

### 前端开发环境

#### 1. 安装依赖

```bash
cd yihua-ccp-frontend
npm install
# 或使用yarn
yarn install
```

#### 2. 配置环境变量

创建 `.env.development` 文件：

```env
# API基础地址
REACT_APP_API_BASE_URL=http://localhost:8080/api/v1

# WebSocket地址
REACT_APP_WS_URL=ws://localhost:8080/ws

# 其他配置
REACT_APP_APP_NAME=易华CCP开发版
REACT_APP_VERSION=1.0.0-dev
```

#### 3. 启动前端服务

```bash
npm start
# 或
yarn start
```

访问 http://localhost:3000 查看前端界面。

## 项目架构

### 后端架构

```
yihua-ccp/
├── src/main/java/com/yihua/ccp/
│   ├── config/          # 配置类
│   ├── controller/      # REST控制器
│   ├── service/         # 业务逻辑层
│   ├── repository/      # 数据访问层
│   ├── entity/          # 实体类
│   ├── dto/             # 数据传输对象
│   ├── util/            # 工具类
│   └── Application.java # 启动类
├── src/main/resources/
│   ├── application.yml  # 主配置文件
│   ├── application-dev.yml   # 开发环境配置
│   ├── application-prod.yml  # 生产环境配置
│   └── db/migration/    # 数据库迁移脚本
└── pom.xml              # Maven配置
```

### 前端架构

```
yihua-ccp-frontend/
├── public/              # 静态资源
├── src/
│   ├── components/      # 可复用组件
│   ├── pages/           # 页面组件
│   ├── services/        # API服务
│   ├── utils/           # 工具函数
│   ├── hooks/           # 自定义Hooks
│   ├── store/           # 状态管理
│   ├── styles/          # 样式文件
│   └── App.js           # 根组件
├── package.json         # 依赖配置
└── .env.example         # 环境变量示例
```

### 技术栈

#### 后端技术栈

- **Spring Boot 2.7+** - 主框架
- **Spring Security** - 安全认证
- **Spring Data JPA** - 数据访问
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存和会话存储
- **RabbitMQ** - 消息队列
- **Elasticsearch** - 搜索引擎
- **JWT** - 身份认证

#### 前端技术栈

- **React 18** - UI框架
- **TypeScript** - 类型安全
- **Ant Design** - UI组件库
- **Redux Toolkit** - 状态管理
- **React Router** - 路由管理
- **Axios** - HTTP客户端
- **Socket.io** - 实时通信

## 开发规范

### 代码规范

#### Java代码规范

```java
// 类命名：使用PascalCase
public class CustomerService {
    
    // 常量：使用UPPER_SNAKE_CASE
    private static final String DEFAULT_GROUP = "DEFAULT_GROUP";
    
    // 变量和方法：使用camelCase
    private CustomerRepository customerRepository;
    
    public Customer createCustomer(CreateCustomerRequest request) {
        // 方法体
    }
}
```

#### JavaScript/TypeScript规范

```typescript
// 接口定义
interface Customer {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

// 组件定义
const CustomerList: React.FC<CustomerListProps> = ({ customers }) => {
  const [loading, setLoading] = useState(false);
  
  const handleCreate = useCallback(async (data: CreateCustomerData) => {
    // 处理逻辑
  }, []);
  
  return (
    <div className="customer-list">
      {/* 组件内容 */}
    </div>
  );
};
```

### API设计规范

#### RESTful API设计

```
GET    /api/v1/customers          # 获取客户列表
POST   /api/v1/customers          # 创建客户
GET    /api/v1/customers/{id}     # 获取客户详情
PUT    /api/v1/customers/{id}     # 更新客户
DELETE /api/v1/customers/{id}     # 删除客户
```

#### 响应格式规范

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 实际数据
  },
  "timestamp": "2024-01-02T10:00:00Z"
}
```

### 数据库设计规范

#### 表命名规范

- 使用小写字母和下划线
- 表名使用复数形式
- 例如：`customers`, `customer_groups`, `message_histories`

#### 字段命名规范

```sql
CREATE TABLE customers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '客户姓名',
    email VARCHAR(255) UNIQUE COMMENT '邮箱地址',
    phone VARCHAR(20) COMMENT '电话号码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

## 插件开发

### 插件架构

易华CCP支持插件化扩展，开发者可以创建自定义插件来扩展系统功能。

#### 插件接口

```java
public interface Plugin {
    String getName();
    String getVersion();
    void initialize(PluginContext context);
    void destroy();
}
```

#### 示例插件

```java
@Component
public class EmailNotificationPlugin implements Plugin {
    
    @Override
    public String getName() {
        return "Email Notification Plugin";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public void initialize(PluginContext context) {
        // 插件初始化逻辑
        context.registerEventHandler("customer.created", this::handleCustomerCreated);
    }
    
    private void handleCustomerCreated(CustomerCreatedEvent event) {
        // 处理客户创建事件
        sendWelcomeEmail(event.getCustomer());
    }
}
```

### 前端插件开发

```typescript
// 插件接口定义
interface FrontendPlugin {
  name: string;
  version: string;
  initialize(context: PluginContext): void;
  destroy(): void;
}

// 示例插件
class CustomDashboardPlugin implements FrontendPlugin {
  name = 'Custom Dashboard Plugin';
  version = '1.0.0';
  
  initialize(context: PluginContext) {
    // 注册自定义组件
    context.registerComponent('CustomDashboard', CustomDashboardComponent);
    
    // 注册菜单项
    context.registerMenuItem({
      key: 'custom-dashboard',
      label: '自定义仪表板',
      path: '/custom-dashboard'
    });
  }
  
  destroy() {
    // 清理资源
  }
}
```

## 测试

### 单元测试

#### 后端测试

```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class CustomerServiceTest {
    
    @Autowired
    private CustomerService customerService;
    
    @MockBean
    private CustomerRepository customerRepository;
    
    @Test
    void shouldCreateCustomer() {
        // Given
        CreateCustomerRequest request = new CreateCustomerRequest();
        request.setName("张三");
        request.setEmail("<EMAIL>");
        
        Customer savedCustomer = new Customer();
        savedCustomer.setId(1L);
        savedCustomer.setName("张三");
        
        when(customerRepository.save(any(Customer.class))).thenReturn(savedCustomer);
        
        // When
        Customer result = customerService.createCustomer(request);
        
        // Then
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("张三");
    }
}
```

#### 前端测试

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { CustomerList } from './CustomerList';

describe('CustomerList', () => {
  it('should render customer list', () => {
    const customers = [
      { id: 1, name: '张三', email: '<EMAIL>' }
    ];
    
    render(<CustomerList customers={customers} />);
    
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
  
  it('should handle customer creation', async () => {
    const onCreateCustomer = jest.fn();
    
    render(<CustomerList customers={[]} onCreateCustomer={onCreateCustomer} />);
    
    fireEvent.click(screen.getByText('新建客户'));
    
    // 填写表单并提交
    fireEvent.change(screen.getByLabelText('姓名'), {
      target: { value: '李四' }
    });
    
    fireEvent.click(screen.getByText('确定'));
    
    expect(onCreateCustomer).toHaveBeenCalledWith({
      name: '李四'
    });
  });
});
```

### 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class CustomerControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldCreateAndRetrieveCustomer() {
        // 创建客户
        CreateCustomerRequest request = new CreateCustomerRequest();
        request.setName("张三");
        request.setEmail("<EMAIL>");
        
        ResponseEntity<Customer> createResponse = restTemplate.postForEntity(
            "/api/v1/customers", request, Customer.class);
        
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        
        Long customerId = createResponse.getBody().getId();
        
        // 获取客户详情
        ResponseEntity<Customer> getResponse = restTemplate.getForEntity(
            "/api/v1/customers/" + customerId, Customer.class);
        
        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(getResponse.getBody().getName()).isEqualTo("张三");
    }
}
```

## 部署和发布

### 构建项目

```bash
# 后端构建
mvn clean package -Dmaven.test.skip=true

# 前端构建
npm run build
```

### Docker构建

```dockerfile
# 后端Dockerfile
FROM openjdk:11-jre-slim

COPY target/yihua-ccp-1.0.0.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```dockerfile
# 前端Dockerfile
FROM nginx:alpine

COPY build/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

### CI/CD配置

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK 11
      uses: actions/setup-java@v2
      with:
        java-version: '11'
        distribution: 'adopt'
    - name: Run tests
      run: mvn test
    
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Build Docker image
      run: docker build -t yihua/ccp:${{ github.sha }} .
    - name: Push to registry
      run: docker push yihua/ccp:${{ github.sha }}
```

## 贡献指南

### 提交代码

1. Fork项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m "Add new feature"`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 代码审查

- 确保代码符合项目规范
- 添加必要的测试用例
- 更新相关文档
- 通过所有自动化测试

更多开发相关问题，请查看[常见问题](faq.md)或加入开发者社区讨论。

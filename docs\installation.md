# 安装部署指南

<!-- 章节统计信息 -->
<div style="background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%); border-left: 4px solid #3fcfbb; padding: 15px 20px; margin: 20px 0; border-radius: 8px;">
  <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
    <span style="color: #2aa698; font-weight: 600; font-size: 0.9em;">📊 本章统计</span>
    <span style="background: #3fcfbb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">字数: ~8,200</span>
    <span style="background: #33cabb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">阅读时长: ~25分钟</span>
    <span style="background: #2aa698; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">难度: 中级</span>
  </div>
</div>

本指南详细介绍了易华CCP的各种安装部署方式，包括单机部署、集群部署和云端部署。

## 系统要求

### 硬件要求

| 部署规模 | CPU | 内存 | 存储 | 网络 |
|---------|-----|------|------|------|
| 小型部署 | 4核 | 8GB | 100GB SSD | 100Mbps |
| 中型部署 | 8核 | 16GB | 500GB SSD | 1Gbps |
| 大型部署 | 16核+ | 32GB+ | 1TB+ SSD | 10Gbps |

### 软件要求

- **操作系统**: 
  - Linux: Ubuntu 18.04+, CentOS 7+, RHEL 7+
  - Windows: Windows Server 2016+
  - macOS: 10.14+ (仅用于开发环境)

- **数据库**: 
  - MySQL 5.7+ 或 8.0+
  - PostgreSQL 10+
  - MongoDB 4.0+ (可选)

- **其他依赖**:
  - Java 11+ (OpenJDK 或 Oracle JDK)
  - Node.js 14+ (前端构建)
  - Redis 5.0+ (缓存)
  - Nginx 1.16+ (反向代理)

## 安装方式

### 方式一：一键安装脚本

最简单的安装方式，适合快速体验和小型部署。

```bash
# 下载安装脚本
curl -fsSL https://install.yihua-ccp.com/install.sh | bash

# 或者手动下载后执行
wget https://install.yihua-ccp.com/install.sh
chmod +x install.sh
./install.sh
```

安装脚本会自动：
- 检查系统环境
- 安装必要依赖
- 下载应用程序
- 配置数据库
- 启动服务

### 方式二：Docker部署

推荐用于开发环境和容器化部署。

#### 单容器部署

```bash
# 拉取镜像
docker pull yihua/ccp:latest

# 运行容器
docker run -d \
  --name yihua-ccp \
  -p 8080:8080 \
  -v /data/yihua-ccp:/app/data \
  yihua/ccp:latest
```

#### Docker Compose部署

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  app:
    image: yihua/ccp:latest
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=db
      - DB_NAME=yihua_ccp
      - DB_USER=root
      - DB_PASSWORD=password
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./data:/app/data

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=yihua_ccp
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

启动服务：

```bash
docker-compose up -d
```

### 方式三：Kubernetes部署

适合大规模生产环境部署。

#### 创建命名空间

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: yihua-ccp
```

#### 部署数据库

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: yihua-ccp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "password"
        - name: MYSQL_DATABASE
          value: "yihua_ccp"
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc
```

#### 部署应用

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yihua-ccp
  namespace: yihua-ccp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yihua-ccp
  template:
    metadata:
      labels:
        app: yihua-ccp
    spec:
      containers:
      - name: yihua-ccp
        image: yihua/ccp:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "mysql"
        - name: DB_NAME
          value: "yihua_ccp"
        - name: DB_USER
          value: "root"
        - name: DB_PASSWORD
          value: "password"
```

### 方式四：手动安装

适合需要自定义配置的高级用户。

#### 1. 准备环境

```bash
# 安装Java
sudo apt update
sudo apt install openjdk-11-jdk

# 安装MySQL
sudo apt install mysql-server
sudo mysql_secure_installation

# 安装Redis
sudo apt install redis-server

# 安装Nginx
sudo apt install nginx
```

#### 2. 下载应用

```bash
# 创建应用目录
sudo mkdir -p /opt/yihua-ccp
cd /opt/yihua-ccp

# 下载应用包
wget https://releases.yihua-ccp.com/v1.0.0/yihua-ccp-1.0.0.tar.gz
tar -xzf yihua-ccp-1.0.0.tar.gz
```

#### 3. 配置数据库

```sql
-- 创建数据库
CREATE DATABASE yihua_ccp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'yihua'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON yihua_ccp.* TO 'yihua'@'localhost';
FLUSH PRIVILEGES;
```

#### 4. 配置应用

编辑配置文件 `config/application.yml`：

```yaml
server:
  port: 8080

spring:
  datasource:
    url: *************************************
    username: yihua
    password: strong_password
    driver-class-name: com.mysql.cj.jdbc.Driver

  redis:
    host: localhost
    port: 6379
    database: 0

logging:
  level:
    com.yihua.ccp: INFO
  file:
    name: logs/yihua-ccp.log
```

#### 5. 启动服务

```bash
# 启动应用
./bin/start.sh

# 或者使用systemd服务
sudo cp scripts/yihua-ccp.service /etc/systemd/system/
sudo systemctl enable yihua-ccp
sudo systemctl start yihua-ccp
```

## 配置优化

### 数据库优化

```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
SET GLOBAL max_connections = 1000;
SET GLOBAL query_cache_size = 268435456;  -- 256MB
```

### JVM优化

```bash
# 在启动脚本中添加JVM参数
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### Nginx配置

```nginx
upstream yihua_ccp {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://yihua_ccp;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 验证安装

### 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查数据库连接
curl http://localhost:8080/health/db

# 检查Redis连接
curl http://localhost:8080/health/redis
```

### 功能测试

1. 访问管理界面：http://localhost:8080
2. 使用默认账户登录：admin/admin
3. 创建测试项目
4. 发送测试消息

## 故障排除

### 常见问题

**服务无法启动**
- 检查端口是否被占用：`netstat -tlnp | grep 8080`
- 检查日志文件：`tail -f logs/yihua-ccp.log`

**数据库连接失败**
- 验证数据库服务状态：`systemctl status mysql`
- 检查连接参数是否正确
- 确认防火墙设置

**内存不足**
- 调整JVM堆内存大小
- 检查系统可用内存：`free -h`
- 优化数据库配置

更多部署相关问题，请参考[常见问题](faq.md)或联系技术支持。

/* 震撼3D青绿色系封面设计 */
.cover {
  background:
    radial-gradient(circle at 30% 20%, rgba(63,207,187,0.8) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(51,202,187,0.6) 0%, transparent 50%),
    linear-gradient(135deg, #2aa698 0%, #3fcfbb 50%, #33cabb 100%) !important;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  perspective: 1200px;
}

/* 震撼3D装饰元素 */
.cover::before {
  content: '';
  position: absolute;
  top: 10%;
  right: 5%;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, rgba(255,255,255,0.15), rgba(63,207,187,0.3));
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  transform: rotateX(45deg) rotateY(45deg) rotateZ(15deg);
  animation: float3D 8s ease-in-out infinite;
  box-shadow:
    0 20px 60px rgba(63,207,187,0.4),
    inset 0 0 50px rgba(255,255,255,0.2);
}

.cover::after {
  content: '';
  position: absolute;
  bottom: 10%;
  left: 5%;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(51,202,187,0.4), rgba(255,255,255,0.2));
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  transform: rotateX(60deg) rotateY(-30deg) rotateZ(-20deg);
  animation: float3D 10s ease-in-out infinite reverse;
  box-shadow:
    0 15px 40px rgba(51,202,187,0.5),
    inset 0 0 30px rgba(255,255,255,0.3);
}

@keyframes float3D {
  0%, 100% {
    transform: rotateX(45deg) rotateY(45deg) rotateZ(15deg) translateY(0px);
  }
  33% {
    transform: rotateX(60deg) rotateY(60deg) rotateZ(25deg) translateY(-15px);
  }
  66% {
    transform: rotateX(30deg) rotateY(30deg) rotateZ(5deg) translateY(-10px);
  }
}

.cover-main {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  padding: 4rem 2rem;
  animation: fadeInUp 1.2s ease-out;
  transform-style: preserve-3d;
}

/* 震撼3D标题效果 */
.cover-main h1 {
  font-size: 5.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  color: #ffffff;
  letter-spacing: 0.05em;
  text-shadow:
    0 1px 0 #33cabb,
    0 2px 0 #2aa698,
    0 3px 0 #258a7a,
    0 4px 0 #1f7a6b,
    0 5px 0 #1a6a5c,
    0 6px 0 #155a4d,
    0 8px 8px rgba(0,0,0,0.3),
    0 12px 20px rgba(42,166,152,0.4),
    0 0 30px rgba(63,207,187,0.6);
  transform: translateZ(80px) rotateX(10deg);
  animation: title3D 4s ease-in-out infinite;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 震撼副标题效果 */
.cover-main h2 {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 1.5rem;
  color: #ffffff;
  letter-spacing: 0.03em;
  text-shadow:
    0 2px 4px rgba(0,0,0,0.4),
    0 4px 8px rgba(42,166,152,0.3),
    0 0 20px rgba(63,207,187,0.4);
  transform: translateZ(40px) rotateX(5deg);
  animation: slideInLeft 1s ease-out 0.3s both;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 引言文字简化 */
.cover-main blockquote {
  font-size: 1.3rem;
  font-style: italic;
  color: rgba(255,255,255,0.95);
  border-left: 3px solid rgba(255,255,255,0.6);
  padding-left: 1.2rem;
  margin: 2rem auto;
  max-width: 450px;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  animation: slideInRight 1s ease-out 0.6s both;
  background: rgba(255,255,255,0.1);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 300;
}

@keyframes title3D {
  0%, 100% {
    transform: translateZ(50px) rotateX(0deg);
  }
  50% {
    transform: translateZ(60px) rotateX(5deg);
  }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 特性标题简化 */
.cover-main p strong {
  display: block;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0,0,0,0.4);
  animation: slideInUp 1s ease-out 0.9s both;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.cover-main ul {
  text-align: left;
  display: inline-block;
  margin: 2rem 0;
  padding: 0;
  list-style: none;
  max-width: 650px;
  perspective: 1000px;
}

/* 震撼3D特性列表 */
.cover-main ul li {
  font-size: 1.15rem;
  margin: 1.5rem 0;
  color: #ffffff;
  line-height: 1.7;
  padding: 1.3rem 2rem;
  background: rgba(255,255,255,0.2);
  border-radius: 16px;
  border-left: 5px solid transparent;
  transition: all 0.5s ease;
  animation: slideInUp 1s ease-out both;
  box-shadow:
    0 10px 30px rgba(0,0,0,0.2),
    0 5px 15px rgba(42,166,152,0.3),
    inset 0 1px 0 rgba(255,255,255,0.3);
  position: relative;
  text-shadow:
    0 1px 2px rgba(0,0,0,0.4),
    0 2px 4px rgba(42,166,152,0.3);
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
  transform: translateZ(20px) rotateX(2deg);
}

.cover-main ul li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(63,207,187,0.15), rgba(51,202,187,0.1));
  border-radius: 15px;
  z-index: -1;
}

.cover-main ul li:nth-child(1) {
  animation-delay: 1.2s;
  border-left-color: #3fcfbb;
  transform: translateZ(15px) rotateY(-2deg);
}
.cover-main ul li:nth-child(2) {
  animation-delay: 1.4s;
  border-left-color: #33cabb;
  transform: translateZ(20px) rotateY(1deg);
}
.cover-main ul li:nth-child(3) {
  animation-delay: 1.6s;
  border-left-color: #2aa698;
  transform: translateZ(18px) rotateY(-1deg);
}
.cover-main ul li:nth-child(4) {
  animation-delay: 1.8s;
  border-left-color: #3fcfbb;
  transform: translateZ(22px) rotateY(2deg);
}
.cover-main ul li:nth-child(5) {
  animation-delay: 2.0s;
  border-left-color: #33cabb;
  transform: translateZ(16px) rotateY(-1.5deg);
}
.cover-main ul li:nth-child(6) {
  animation-delay: 2.2s;
  border-left-color: #2aa698;
  transform: translateZ(19px) rotateY(1.5deg);
}

/* 特性列表项悬停效果优化 */
.cover-main ul li:hover {
  background: rgba(63,207,187,0.45);
  transform: translateZ(35px) rotateY(0deg) scale(1.03);
  box-shadow:
    0 18px 45px rgba(42,166,152,0.5),
    inset 0 1px 0 rgba(255,255,255,0.4);
  text-shadow:
    0 1px 3px rgba(42,166,152,0.8),
    0 2px 6px rgba(0,0,0,0.4);
}

/* emoji和文字对齐优化 */
.cover-main ul li strong {
  display: inline-block;
  margin-right: 0.5rem;
  vertical-align: middle;
}

.cover-main ul li::before {
  content: '';
  display: inline-block;
  width: 0.5rem;
  vertical-align: middle;
}

/* 震撼3D按钮效果 */
.cover-main p:last-child {
  animation: slideInUp 1s ease-out 2.5s both;
  perspective: 1000px;
}

.cover-main p:last-child a {
  display: inline-block;
  margin: 1rem 1.5rem;
  padding: 1.4rem 3.5rem;
  text-decoration: none;
  border-radius: 20px;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.5s ease;
  border: none;
  position: relative;
  overflow: hidden;
  transform: translateZ(30px) rotateX(5deg);
  box-shadow:
    0 15px 40px rgba(0,0,0,0.3),
    0 8px 20px rgba(42,166,152,0.2),
    inset 0 1px 0 rgba(255,255,255,0.3);
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: 0.02em;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.cover-main p:last-child a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 15px;
}

.cover-main p:last-child a:hover::before {
  opacity: 1;
}

/* 第一个按钮 - 震撼主要按钮 */
.cover-main p:last-child a:nth-child(1) {
  background: linear-gradient(135deg, #3fcfbb 0%, #33cabb 50%, #2aa698 100%);
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.cover-main p:last-child a:nth-child(1):hover {
  background: linear-gradient(135deg, #4dd4c7 0%, #3fcfbb 50%, #33cabb 100%);
  transform: translateZ(50px) rotateX(-10deg) scale(1.05);
  box-shadow:
    0 25px 60px rgba(63, 207, 187, 0.6),
    0 15px 30px rgba(42,166,152,0.4),
    inset 0 2px 0 rgba(255,255,255,0.4);
  text-shadow: 0 3px 6px rgba(0,0,0,0.6);
}

/* 第二个按钮 - 震撼次要按钮 */
.cover-main p:last-child a:nth-child(2) {
  background: rgba(255,255,255,0.15);
  color: #ffffff;
  border: 3px solid rgba(255,255,255,0.7);
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.cover-main p:last-child a:nth-child(2):hover {
  background: rgba(255,255,255,0.25);
  border-color: rgba(255,255,255,0.9);
  transform: translateZ(50px) rotateX(-10deg) scale(1.05);
  box-shadow:
    0 25px 60px rgba(255,255,255, 0.3),
    0 15px 30px rgba(255,255,255,0.2),
    inset 0 2px 0 rgba(255,255,255,0.4);
  text-shadow: 0 3px 6px rgba(0,0,0,0.6);
}

/* 第三个按钮 - 震撼GitHub按钮 */
.cover-main p:last-child a:nth-child(3) {
  background: linear-gradient(135deg, #2aa698 0%, #258a7a 50%, #1f7a6b 100%);
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.cover-main p:last-child a:nth-child(3):hover {
  background: linear-gradient(135deg, #33b5a5 0%, #2aa698 50%, #258a7a 100%);
  transform: translateZ(50px) rotateX(-10deg) scale(1.05);
  box-shadow:
    0 25px 60px rgba(42, 166, 152, 0.6),
    0 15px 30px rgba(37,138,122,0.4),
    inset 0 2px 0 rgba(255,255,255,0.4);
  text-shadow: 0 3px 6px rgba(0,0,0,0.6);
}

@keyframes title3D {
  0%, 100% {
    transform: translateZ(80px) rotateX(10deg);
  }
  50% {
    transform: translateZ(90px) rotateX(15deg);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移除复杂的统计和动画样式 */

/* 额外的动态装饰元素 */
.cover-main::before {
  content: '✨';
  position: absolute;
  top: 20%;
  right: 15%;
  font-size: 2rem;
  animation: twinkle 3s ease-in-out infinite;
  z-index: 5;
}

.cover-main::after {
  content: '🚀';
  position: absolute;
  bottom: 20%;
  left: 10%;
  font-size: 2rem;
  animation: bounce 2s ease-in-out infinite;
  z-index: 5;
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px) rotate(-10deg);
  }
  50% {
    transform: translateY(-15px) rotate(10deg);
  }
}

/* 鼠标悬停时的整体效果 */
.cover:hover .cover-main h1 {
  animation-duration: 2s;
}

.cover:hover .cover::before {
  animation-duration: 10s;
}

/* 侧边栏样式优化 - 青绿色系 */
.sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.sidebar-nav li a {
  color: #495057;
  font-weight: 500;
  transition: color 0.3s ease;
}

.sidebar-nav li a:hover {
  color: #3fcfbb;
}

.sidebar-nav li.active > a {
  color: #3fcfbb;
  font-weight: 600;
  border-right: 3px solid #3fcfbb;
}

/* 内容区域样式 */
.content {
  padding-top: 60px;
}

/* 搜索框样式 - 青绿色系 */
.search input {
  border: 2px solid #e9ecef;
  border-radius: 25px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.search input:focus {
  border-color: #3fcfbb;
  outline: none;
  box-shadow: 0 0 0 3px rgba(63, 207, 187, 0.2);
}

/* 代码块样式优化 */
pre[data-lang] {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 表格样式 - 青绿色系 */
table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(42,166,152,0.1);
}

table th {
  background: linear-gradient(135deg, #3fcfbb, #33cabb);
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

table tr:hover {
  background-color: rgba(63,207,187,0.1);
}

/* 文档内容链接样式 */
.content a {
  color: #3fcfbb;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.content a:hover {
  color: #2aa698;
  border-bottom-color: #3fcfbb;
}

/* 代码块内联样式 */
.content code {
  background: rgba(63,207,187,0.1);
  color: #2aa698;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-weight: 500;
}

/* 引用块样式 */
.content blockquote {
  border-left: 4px solid #3fcfbb;
  background: rgba(63,207,187,0.05);
  margin: 1rem 0;
  padding: 1rem 1.5rem;
  border-radius: 0 8px 8px 0;
}

.content blockquote p {
  color: #2aa698;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cover-main {
    padding: 2rem 1rem;
  }

  .cover-main h1 {
    font-size: 2.8rem;
  }

  .cover-main h2 {
    font-size: 1.3rem;
  }

  .cover-main ul {
    max-width: 100%;
    padding: 0 1rem;
  }

  .cover-main ul li {
    font-size: 1rem;
    padding: 0.6rem 1rem;
    margin: 0.8rem 0;
  }

  .cover-main p:last-child a {
    display: block;
    margin: 0.8rem auto;
    max-width: 280px;
    padding: 0.8rem 2rem;
  }

  /* 减少动画延迟 */
  .cover-main ul li:nth-child(1) { animation-delay: 0.8s; }
  .cover-main ul li:nth-child(2) { animation-delay: 1.0s; }
  .cover-main ul li:nth-child(3) { animation-delay: 1.2s; }
  .cover-main ul li:nth-child(4) { animation-delay: 1.4s; }
  .cover-main ul li:nth-child(5) { animation-delay: 1.6s; }
  .cover-main ul li:nth-child(6) { animation-delay: 1.8s; }
}

@media (max-width: 480px) {
  .cover-main {
    padding: 1.5rem 0.5rem;
  }

  .cover-main h1 {
    font-size: 2.2rem;
  }

  .cover-main h2 {
    font-size: 1.1rem;
  }

  .cover-main blockquote {
    font-size: 1.1rem;
    margin: 1.5rem auto;
  }

  .cover-main ul li {
    font-size: 0.95rem;
    padding: 0.5rem 0.8rem;
  }

  .cover-main p:last-child a {
    padding: 0.7rem 1.5rem;
    font-size: 0.9rem;
  }

  /* 简化动画效果 */
  .cover::before {
    animation-duration: 30s;
  }

  .cover::after {
    display: none;
  }
}
